import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Query, Mutation, Resolver } from '@nestjs/graphql';
import { SalesOrderService } from './sales-order.service';
import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { AuthData } from '@types';
import {
  SenangPayFormDto,
  PaymentStatusDto,
  SalesOrderDto,
  BillplzFormDto,
  SubscriptionActivationResultDto
} from './dto/sales-order.gql.dto';

@UseGuards(GqlAuthGuard)
@Resolver()
export class SalesOrderResolver {
  constructor(private salesOrderService: SalesOrderService) {}

  @Query(() => [SenangPayFormDto])
  async createSenangPayForm(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('subscriptionPackageId') subscriptionPackageId: string
  ): Promise<SenangPayFormDto[]> {
    return await this.salesOrderService.createSenangPayForm(user.id, subscriptionPackageId);
  }

  @Query(() => [SenangPayFormDto])
  async createSenangPayPaymentForm(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('orderId') orderId: string
  ): Promise<SenangPayFormDto[]> {
    return await this.salesOrderService.createSenangPayPaymentForm(user.id, orderId);
  }

  @Query(() => PaymentStatusDto)
  async checkSenangPayPaymentStatus(
    @Args('orderId') orderId: string,
    @Args('transactionId') transactionId: string,
    @Args('hash') hash: string,
    @Args('msg') msg: string
  ): Promise<PaymentStatusDto> {
    return await this.salesOrderService.checkSenangPayPaymentStatus(orderId, transactionId, hash, msg);
  }

  @Query(() => SalesOrderDto)
  async getInvoice(@GqlGetGqlAuthData() user: AuthData, @Args('cuid') cuid: string): Promise<SalesOrderDto> {
    return await this.salesOrderService.getInvoice(user.id, cuid);
  }

  @UseGuards(GqlAuthGuard)
  @Query(() => String, { name: 'getBillplzPaymentConfirmation' })
  async getBillplzPaymentConfirmation(
    @Args('billId') billId: string
  ): Promise<string> {
    return await this.salesOrderService.getBillplzStatus(billId);
  }


  @Mutation(() => BillplzFormDto)
  async createBillplzForm(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('subscriptionPackageId') subscriptionPackageId: string,
    @Args('teamSize') teamSize: number,
    @Args('isYearly') isYearly: boolean
  ): Promise<BillplzFormDto> {
    return await this.salesOrderService.createBillplzForm(user.id, subscriptionPackageId, teamSize, isYearly);
  }

  @Mutation(() => SubscriptionActivationResultDto)
  async activateSubscriptionWithDeferredPayment(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('subscriptionPackageId') subscriptionPackageId: string,
    @Args('teamSize') teamSize: number
  ): Promise<SubscriptionActivationResultDto> {
    return await this.salesOrderService.activateSubscriptionWithDeferredPayment(user.id, subscriptionPackageId, teamSize);
  }
}
