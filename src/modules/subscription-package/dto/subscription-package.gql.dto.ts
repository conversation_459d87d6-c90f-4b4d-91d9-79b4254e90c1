import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { SubscriptionPackageEntity } from '../entity/subscription-package.entity';

/**
 * Enum for pricing calculation types
 */
export enum PricingCalculationType {
  NEW_SUBSCRIPTION = 'new_subscription',
  ADD_MEMBER = 'add_member'
}

registerEnumType(PricingCalculationType, {
  name: 'PricingCalculationType',
  description: 'Type of pricing calculation to perform'
});

@ObjectType('SubscriptionPackage')
@QueryOptions({ ...defaultQueryOptions })
export class SubscriptionPackageDto extends SubscriptionPackageEntity {}

@InputType()
export class CreateSubscriptionPackageInputDTO {
  title: string;
  description: string;
  amount: number;
  availableDuration: number;
}

@InputType()
export class UpdateSubscriptionPackageInputDTO extends PartialType(CreateSubscriptionPackageInputDTO) {}

/**
 * Input DTO for pricing calculation requests
 */
@InputType('PricingCalculationInput')
export class PricingCalculationInputDto {
  @Field(() => ID, { description: 'Subscription package ID' })
  subscriptionPackageId: string;

  @Field(() => Number, { description: 'Number of team members/seats' })
  teamSize: number;

  @Field(() => Boolean, { description: 'Whether billing is yearly (true) or monthly (false)' })
  isYearly: boolean;

  @Field(() => Date, {
    nullable: true,
    description: 'Billing start date (defaults to current date if not provided)'
  })
  billingDate?: Date;

  @Field(() => PricingCalculationType, {
    nullable: true,
    defaultValue: PricingCalculationType.NEW_SUBSCRIPTION,
    description: 'Type of pricing calculation (new_subscription or add_member)'
  })
  type?: PricingCalculationType;

  @Field(() => Number, {
    nullable: true,
    description: 'Current seat count (required when type is add_member)'
  })
  currentSeatCount?: number;
}

/**
 * Billing period details
 */
@ObjectType('BillingPeriod')
export class BillingPeriodDto {
  @Field(() => Date, { description: 'Period start date' })
  startDate: Date;

  @Field(() => Date, { description: 'Period end date' })
  endDate: Date;

  @Field(() => Number, { description: 'Base amount in cents' })
  baseAmountInCents: number;

  @Field(() => Number, { description: 'SST amount in cents' })
  sstAmountInCents: number;

  @Field(() => Number, { description: 'Total amount in cents' })
  totalAmountInCents: number;

  @Field(() => Number, { description: 'Base amount in RM' })
  baseAmount: number;

  @Field(() => Number, { description: 'SST amount in RM' })
  sstAmount: number;

  @Field(() => Number, { description: 'Total amount in RM' })
  totalAmount: number;

  @Field(() => Boolean, { description: 'Whether this period is prorated' })
  isProrated: boolean;

  @Field(() => String, { description: 'Human-readable label for this period' })
  label: string;

  @Field(() => String, { description: 'Formatted start date for display' })
  formattedStartDate: string;

  @Field(() => String, { description: 'Formatted end date for display' })
  formattedEndDate: string;
}

/**
 * Full month billing period details (without isProrated field)
 */
@ObjectType('FullMonthBillingPeriod')
export class FullMonthBillingPeriodDto {
  @Field(() => Date, { description: 'Period start date' })
  startDate: Date;

  @Field(() => Date, { description: 'Period end date' })
  endDate: Date;

  @Field(() => Number, { description: 'Base amount in cents' })
  baseAmountInCents: number;

  @Field(() => Number, { description: 'SST amount in cents' })
  sstAmountInCents: number;

  @Field(() => Number, { description: 'Total amount in cents' })
  totalAmountInCents: number;

  @Field(() => Number, { description: 'Base amount in RM' })
  baseAmount: number;

  @Field(() => Number, { description: 'SST amount in RM' })
  sstAmount: number;

  @Field(() => Number, { description: 'Total amount in RM' })
  totalAmount: number;

  @Field(() => String, { description: 'Formatted start date for display' })
  formattedStartDate: string;

  @Field(() => String, { description: 'Formatted end date for display' })
  formattedEndDate: string;
}

/**
 * Combined billing amounts
 */
@ObjectType('CombinedBillingAmounts')
export class CombinedBillingAmountsDto {
  @Field(() => Number, { description: 'Combined base amount in cents' })
  baseAmountInCents: number;

  @Field(() => Number, { description: 'Combined SST amount in cents' })
  sstAmountInCents: number;

  @Field(() => Number, { description: 'Combined total amount in cents' })
  totalAmountInCents: number;

  @Field(() => Number, { description: 'Combined base amount in RM' })
  baseAmount: number;

  @Field(() => Number, { description: 'Combined SST amount in RM' })
  sstAmount: number;

  @Field(() => Number, { description: 'Combined total amount in RM' })
  totalAmount: number;
}

/**
 * Current period prorated details
 */
@ObjectType('CurrentPeriodProrated')
export class CurrentPeriodProratedDto {
  @Field(() => Number, { description: 'Prorated amount for current period' })
  amount: number;

  @Field(() => String, { description: 'Start date of current period' })
  startDate: string;

  @Field(() => String, { description: 'End date of current period' })
  endDate: string;
}

/**
 * Next period details
 */
@ObjectType('NextPeriod')
export class NextPeriodDto {
  @Field(() => Number, { description: 'Amount for next period' })
  amount: number;

  @Field(() => String, { description: 'Start date of next period' })
  startDate: string;

  @Field(() => String, { description: 'End date of next period' })
  endDate: string;
}

/**
 * Yearly savings details
 */
@ObjectType('YearlySavings')
export class YearlySavingsDto {
  @Field(() => Number, { description: 'Percentage saved with yearly billing' })
  percentage: number;

  @Field(() => Number, { description: 'Amount saved with yearly billing' })
  amountSaved: number;
}

/**
 * Pricing breakdown details
 */
@ObjectType('PricingBreakdown')
export class PricingBreakdownDto {
  @Field(() => Number, { description: 'Base price per member' })
  basePrice: number;

  @Field(() => Number, { description: 'Number of members' })
  memberCount: number;

  @Field(() => Number, { description: 'Subtotal before SST' })
  subtotal: number;

  @Field(() => Number, { description: 'SST amount' })
  sst: number;

  @Field(() => Number, { description: 'Total amount including SST' })
  total: number;
}

/**
 * Detailed pricing information
 */
@ObjectType('PricingDetails')
export class PricingDetailsDto {
  @Field(() => Number, { description: 'Price per member' })
  pricePerMember: number;

  @Field(() => CurrentPeriodProratedDto, { description: 'Current period prorated details' })
  currentPeriodProrated: CurrentPeriodProratedDto;

  @Field(() => NextPeriodDto, { description: 'Next period details' })
  nextPeriod: NextPeriodDto;

  @Field(() => YearlySavingsDto, {
    nullable: true,
    description: 'Yearly savings details (only for yearly subscriptions)'
  })
  yearlySavings?: YearlySavingsDto;

  @Field(() => PricingBreakdownDto, { description: 'Pricing breakdown' })
  breakdown: PricingBreakdownDto;
}

/**
 * Period cost breakdown DTO
 */
@ObjectType('PeriodCostBreakdown')
export class PeriodCostBreakdownDto {
  @Field(() => Number, { description: 'Base amount before taxes and discounts' })
  baseAmount: number;

  @Field(() => Number, { description: 'Discount amount (if any)' })
  discountAmount: number;

  @Field(() => Number, { description: 'Amount after discount, before tax' })
  subtotalAmount: number;

  @Field(() => Number, { description: 'Tax amount (SST)' })
  taxAmount: number;

  @Field(() => Number, { description: 'Final total amount' })
  totalAmount: number;

  @Field(() => Number, { description: 'Tax rate applied (e.g., 0.06 for 6% SST)' })
  taxRate: number;

  @Field(() => Number, { description: 'Discount rate applied (e.g., 0.10 for 10% discount)' })
  discountRate: number;
}

/**
 * Pricing period DTO
 */
@ObjectType('PricingPeriod')
export class PricingPeriodDto {
  @Field(() => String, { description: 'Start date of the billing period (formatted)' })
  startDate: string;

  @Field(() => String, { description: 'End date of the billing period (formatted)' })
  endDate: string;

  @Field(() => String, { description: 'Duration description (e.g., "Partial Month", "Full Month", "Annual")' })
  description: string;

  @Field(() => Boolean, { description: 'Whether this period is prorated' })
  isProrated: boolean;

  @Field(() => PeriodCostBreakdownDto, { description: 'Detailed cost breakdown for this period' })
  costs: PeriodCostBreakdownDto;
}

/**
 * Member pricing DTO
 */
@ObjectType('MemberPricing')
export class MemberPricingDto {
  @Field(() => Number, { description: 'Original price per member per month' })
  originalPricePerMember: number;

  @Field(() => Number, { description: 'Discounted price per member per month (after yearly discount)' })
  discountedPricePerMember: number;

  @Field(() => Number, { description: 'Effective monthly price per member (considering billing frequency)' })
  effectiveMonthlyPricePerMember: number;

  @Field(() => Number, { description: 'Current team size' })
  currentTeamSize: number;

  @Field(() => Number, { nullable: true, description: 'New team size (if changing membership)' })
  newTeamSize?: number;
}

/**
 * Savings info DTO
 */
@ObjectType('SavingsInfo')
export class SavingsInfoDto {
  @Field(() => Number, { description: 'Monthly cost if billed monthly' })
  monthlyBillingTotal: number;

  @Field(() => Number, { description: 'Yearly cost if billed yearly' })
  yearlyBillingTotal: number;

  @Field(() => Number, { description: 'Amount saved per year' })
  annualSavings: number;

  @Field(() => Number, { description: 'Percentage saved' })
  savingsPercentage: number;

  @Field(() => Number, { description: 'Amount saved per month' })
  monthlySavings: number;
}

/**
 * Payment schedule DTO
 */
@ObjectType('PaymentSchedule')
export class PaymentScheduleDto {
  @Field(() => String, { description: 'Next payment date' })
  nextPaymentDate: string;

  @Field(() => Number, { description: 'Next payment amount' })
  nextPaymentAmount: number;

  @Field(() => String, { description: 'Billing frequency (monthly/yearly)' })
  billingFrequency: string;

  @Field(() => String, { description: 'Subscription end date' })
  subscriptionEndDate: string;

  @Field(() => Number, { description: 'Days until next payment' })
  daysUntilNextPayment: number;

  @Field(() => Number, { nullable: true, description: 'Ongoing monthly subscription amount (for member changes)' })
  ongoingMonthlyAmount?: number;

  @Field(() => Number, { nullable: true, description: 'Previous monthly amount (before member change)' })
  previousMonthlyAmount?: number;
}

/**
 * Subscription info DTO
 */
@ObjectType('SubscriptionInfo')
export class SubscriptionInfoDto {
  @Field(() => String, { description: 'Subscription package ID' })
  packageId: string;

  @Field(() => String, { description: 'Package name' })
  packageName: string;

  @Field(() => Boolean, { description: 'Whether this is a yearly subscription' })
  isYearly: boolean;

  @Field(() => String, { description: 'Subscription type (new, upgrade, member_change, etc.)' })
  calculationType: string;
}

/**
 * Billing periods DTO
 */
@ObjectType('BillingPeriods')
export class BillingPeriodsDto {
  @Field(() => PricingPeriodDto, { description: 'Current/First billing period (may be prorated)' })
  currentPeriod: PricingPeriodDto;

  @Field(() => PricingPeriodDto, { description: 'Next/Ongoing billing period (full period)' })
  nextPeriod: PricingPeriodDto;

  @Field(() => PeriodCostBreakdownDto, { description: 'Combined totals for display purposes' })
  combinedTotals: PeriodCostBreakdownDto;
}

/**
 * Context info DTO
 */
@ObjectType('ContextInfo')
export class ContextInfoDto {
  @Field(() => String, { description: 'Currency code' })
  currency: string;

  @Field(() => String, { description: 'Timezone for date calculations' })
  timezone: string;

  @Field(() => String, { description: 'Calculation timestamp' })
  calculatedAt: string;
}

/**
 * Complete comprehensive pricing calculation result
 */
@ObjectType('ComprehensivePricingCalculation')
export class ComprehensivePricingCalculationDto {
  @Field(() => SubscriptionInfoDto, { description: 'Basic subscription information' })
  subscription: SubscriptionInfoDto;

  @Field(() => MemberPricingDto, { description: 'Per-member pricing details' })
  memberPricing: MemberPricingDto;

  @Field(() => BillingPeriodsDto, { description: 'Billing periods breakdown' })
  periods: BillingPeriodsDto;

  @Field(() => SavingsInfoDto, { nullable: true, description: 'Savings information (for yearly subscriptions)' })
  savings?: SavingsInfoDto;

  @Field(() => PaymentScheduleDto, { description: 'Payment schedule' })
  paymentSchedule: PaymentScheduleDto;

  @Field(() => ContextInfoDto, { description: 'Additional context' })
  context: ContextInfoDto;
}

/**
 * Legacy pricing calculation result (for backward compatibility)
 */
@ObjectType('PricingCalculationResult')
export class PricingCalculationResultDto {
  @Field(() => BillingPeriodDto, { description: 'First period billing details (prorated or initial month)' })
  firstPeriod: BillingPeriodDto;

  @Field(() => FullMonthBillingPeriodDto, { description: 'Full month period billing details' })
  fullMonthPeriod: FullMonthBillingPeriodDto;

  @Field(() => CombinedBillingAmountsDto, { description: 'Combined amounts for both periods' })
  combined: CombinedBillingAmountsDto;

  @Field(() => Date, { description: 'Subscription end date' })
  subscriptionEndDate: Date;

  @Field(() => String, { description: 'Formatted subscription end date for display' })
  formattedSubscriptionEndDate: string;

  @Field(() => Boolean, { description: 'Whether billing starts on the first day of the month' })
  isFirstDayOfMonth: boolean;

  @Field(() => Boolean, { description: 'Whether billing starts after the 14th of the month' })
  isAfterMidMonth: boolean;

  @Field(() => Number, { description: 'Day of the month when billing starts' })
  dayOfMonth: number;

  @Field(() => Number, { description: 'Team size used in calculation' })
  teamSize: number;

  @Field(() => Boolean, { description: 'Whether yearly billing was applied' })
  isYearly: boolean;

  @Field(() => Number, { description: 'Monthly price per member before discount' })
  monthlyPricePerMember: number;

  @Field(() => Number, { description: 'Discounted monthly price per member (if yearly)' })
  discountedMonthlyPricePerMember: number;

  @Field(() => Number, { description: 'Yearly discount rate applied (if applicable)' })
  yearlyDiscountRate: number;

  @Field(() => PricingDetailsDto, { description: 'Detailed pricing information for frontend' })
  pricingDetails: PricingDetailsDto;

  @Field(() => ComprehensivePricingCalculationDto, { description: 'New comprehensive pricing structure' })
  comprehensive: ComprehensivePricingCalculationDto;
}
