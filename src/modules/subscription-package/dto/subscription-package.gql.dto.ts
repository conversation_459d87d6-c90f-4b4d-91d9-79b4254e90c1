import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { SubscriptionPackageEntity } from '../entity/subscription-package.entity';

/**
 * Enum for pricing calculation types
 */
export enum PricingCalculationType {
  NEW_SUBSCRIPTION = 'new_subscription',
  ADD_MEMBER = 'add_member'
}

registerEnumType(PricingCalculationType, {
  name: 'PricingCalculationType',
  description: 'Type of pricing calculation to perform'
});

@ObjectType('SubscriptionPackage')
@QueryOptions({ ...defaultQueryOptions })
export class SubscriptionPackageDto extends SubscriptionPackageEntity {}

@InputType()
export class CreateSubscriptionPackageInputDTO {
  title: string;
  description: string;
  amount: number;
  availableDuration: number;
}

@InputType()
export class UpdateSubscriptionPackageInputDTO extends PartialType(CreateSubscriptionPackageInputDTO) {}

/**
 * Input DTO for pricing calculation requests
 */
@InputType('PricingCalculationInput')
export class PricingCalculationInputDto {
  @Field(() => ID, { description: 'Subscription package ID' })
  subscriptionPackageId: string;

  @Field(() => Number, { description: 'Number of team members/seats' })
  teamSize: number;

  @Field(() => Boolean, { description: 'Whether billing is yearly (true) or monthly (false)' })
  isYearly: boolean;

  @Field(() => Date, {
    nullable: true,
    description: 'Billing start date (defaults to current date if not provided)'
  })
  billingDate?: Date;

  @Field(() => PricingCalculationType, {
    nullable: true,
    defaultValue: PricingCalculationType.NEW_SUBSCRIPTION,
    description: 'Type of pricing calculation (new_subscription or add_member)'
  })
  type?: PricingCalculationType;

  @Field(() => Number, {
    nullable: true,
    description: 'Current seat count (required when type is add_member)'
  })
  currentSeatCount?: number;
}

/**
 * Billing period details
 */
@ObjectType('BillingPeriod')
export class BillingPeriodDto {
  @Field(() => Date, { description: 'Period start date' })
  startDate: Date;

  @Field(() => Date, { description: 'Period end date' })
  endDate: Date;

  @Field(() => Number, { description: 'Base amount in cents' })
  baseAmountInCents: number;

  @Field(() => Number, { description: 'SST amount in cents' })
  sstAmountInCents: number;

  @Field(() => Number, { description: 'Total amount in cents' })
  totalAmountInCents: number;

  @Field(() => Number, { description: 'Base amount in RM' })
  baseAmount: number;

  @Field(() => Number, { description: 'SST amount in RM' })
  sstAmount: number;

  @Field(() => Number, { description: 'Total amount in RM' })
  totalAmount: number;

  @Field(() => Boolean, { description: 'Whether this period is prorated' })
  isProrated: boolean;

  @Field(() => String, { description: 'Human-readable label for this period' })
  label: string;

  @Field(() => String, { description: 'Formatted start date for display' })
  formattedStartDate: string;

  @Field(() => String, { description: 'Formatted end date for display' })
  formattedEndDate: string;
}

/**
 * Full month billing period details (without isProrated field)
 */
@ObjectType('FullMonthBillingPeriod')
export class FullMonthBillingPeriodDto {
  @Field(() => Date, { description: 'Period start date' })
  startDate: Date;

  @Field(() => Date, { description: 'Period end date' })
  endDate: Date;

  @Field(() => Number, { description: 'Base amount in cents' })
  baseAmountInCents: number;

  @Field(() => Number, { description: 'SST amount in cents' })
  sstAmountInCents: number;

  @Field(() => Number, { description: 'Total amount in cents' })
  totalAmountInCents: number;

  @Field(() => Number, { description: 'Base amount in RM' })
  baseAmount: number;

  @Field(() => Number, { description: 'SST amount in RM' })
  sstAmount: number;

  @Field(() => Number, { description: 'Total amount in RM' })
  totalAmount: number;

  @Field(() => String, { description: 'Formatted start date for display' })
  formattedStartDate: string;

  @Field(() => String, { description: 'Formatted end date for display' })
  formattedEndDate: string;
}

/**
 * Combined billing amounts
 */
@ObjectType('CombinedBillingAmounts')
export class CombinedBillingAmountsDto {
  @Field(() => Number, { description: 'Combined base amount in cents' })
  baseAmountInCents: number;

  @Field(() => Number, { description: 'Combined SST amount in cents' })
  sstAmountInCents: number;

  @Field(() => Number, { description: 'Combined total amount in cents' })
  totalAmountInCents: number;

  @Field(() => Number, { description: 'Combined base amount in RM' })
  baseAmount: number;

  @Field(() => Number, { description: 'Combined SST amount in RM' })
  sstAmount: number;

  @Field(() => Number, { description: 'Combined total amount in RM' })
  totalAmount: number;
}

/**
 * Complete pricing calculation result
 */
@ObjectType('PricingCalculationResult')
export class PricingCalculationResultDto {
  @Field(() => BillingPeriodDto, { description: 'First period billing details (prorated or initial month)' })
  firstPeriod: BillingPeriodDto;

  @Field(() => FullMonthBillingPeriodDto, { description: 'Full month period billing details' })
  fullMonthPeriod: FullMonthBillingPeriodDto;

  @Field(() => CombinedBillingAmountsDto, { description: 'Combined amounts for both periods' })
  combined: CombinedBillingAmountsDto;

  @Field(() => Date, { description: 'Subscription end date' })
  subscriptionEndDate: Date;

  @Field(() => String, { description: 'Formatted subscription end date for display' })
  formattedSubscriptionEndDate: string;

  @Field(() => Boolean, { description: 'Whether billing starts on the first day of the month' })
  isFirstDayOfMonth: boolean;

  @Field(() => Boolean, { description: 'Whether billing starts after the 14th of the month' })
  isAfterMidMonth: boolean;

  @Field(() => Number, { description: 'Day of the month when billing starts' })
  dayOfMonth: number;

  @Field(() => Number, { description: 'Team size used in calculation' })
  teamSize: number;

  @Field(() => Boolean, { description: 'Whether yearly billing was applied' })
  isYearly: boolean;

  @Field(() => Number, { description: 'Monthly price per member before discount' })
  monthlyPricePerMember: number;

  @Field(() => Number, { description: 'Discounted monthly price per member (if yearly)' })
  discountedMonthlyPricePerMember: number;

  @Field(() => Number, { description: 'Yearly discount rate applied (if applicable)' })
  yearlyDiscountRate: number;
}
