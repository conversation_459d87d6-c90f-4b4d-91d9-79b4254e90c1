import { Injectable, Logger } from '@nestjs/common';
import { Resolver, ResolveField, Parent } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompanySubscriptionDto } from './dto/company-subscription.gql.dto';
import { CompanySubscriptionEntity } from './entity/company-subscription.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { calculateSubscriptionPrice, calculateComprehensivePricing } from '@common/pricing-helper';
import moment from 'moment';

@Injectable()
@Resolver(() => CompanySubscriptionDto)
export class CompanySubscriptionResolver {
  private readonly logger = new Logger(CompanySubscriptionResolver.name);

  constructor(
    @InjectRepository(CompanySubscriptionEntity)
    private readonly companySubscriptionRepo: Repository<CompanySubscriptionEntity>,
    @InjectRepository(SubscriptionPackageEntity)
    private readonly subscriptionPackageRepo: Repository<SubscriptionPackageEntity>
  ) {}

  /**
   * Custom resolver for nextBillingAmount that ensures subscriptionPackage is loaded
   * This resolver will be called whenever nextBillingAmount is requested in a GraphQL query
   */
  @ResolveField('nextBillingAmount', () => Number, { nullable: true })
  async nextBillingAmount(@Parent() subscription: CompanySubscriptionDto): Promise<number | null> {
    try {
      const isActive = this.isSubscriptionActive(subscription);
      if (!isActive) {
        this.logger.debug(`Subscription ${subscription.id} is not active`);
        return null;
      }

      // Load subscription package if not already loaded
      let subscriptionPackage = subscription.subscriptionPackage;
      if (!subscriptionPackage) {
        subscriptionPackage = await this.subscriptionPackageRepo.findOne({
          where: { id: subscription.subscriptionPackageId }
        });

        if (!subscriptionPackage) {
          this.logger.error(`Subscription package not found for subscription ${subscription.id}`);
          return null;
        }
      }



      // Calculate the next billing amount using the pricing helper
      // For next billing, we always want the full monthly amount regardless of current proration
      const pricingResult = calculateSubscriptionPrice(
        subscriptionPackage,
        subscription.seatCount || 1,
        subscription.isYearly || false,
        new Date(2024, 0, 1) // Use 1st of month to ensure no proration for next billing calculation
      );

      // Use the first period amount since we forced no proration for next billing calculation
      const totalAmount = pricingResult.firstPeriod.totalAmount;

      // Apply credit balance
      const creditBalance = subscription.creditBalance || 0;
      const finalAmount = Math.max(0, totalAmount - creditBalance);

      return finalAmount;

    } catch (error) {
      this.logger.error(`Error calculating nextBillingAmount for subscription ${subscription.id}:`, error);
      return null;
    }
  }

  /**
   * Resolver for isSubscriptionActive computed field
   */
  @ResolveField('isSubscriptionActive', () => Boolean, { nullable: true })
  isSubscriptionActive(@Parent() subscription: CompanySubscriptionDto): boolean {
    try {
      if (!subscription.subscriptionEndDate) return false;
      return subscription.subscriptionEndDate > moment().subtract(1, "days").toDate();
    } catch (error) {
      this.logger.error('Error in isSubscriptionActive resolver:', error);
      return false;
    }
  }

  /**
   * Resolver for isSubscriptionInGracePeriod computed field
   * 10 DAYS GRACE PERIOD AFTER END OF SUBSCRIPTION PERIOD
   */
  @ResolveField('isSubscriptionInGracePeriod', () => Boolean, { nullable: true })
  isSubscriptionInGracePeriod(@Parent() subscription: CompanySubscriptionDto): boolean {
    try {
      if (!subscription.subscriptionEndDate) return false;
      return new Date() > subscription.subscriptionEndDate && subscription.subscriptionEndDate > moment().subtract(10, "days").toDate();
    } catch (error) {
      this.logger.error('Error in isSubscriptionInGracePeriod resolver:', error);
      return false;
    }
  }

  /**
   * Resolver for isTrialEnding computed field
   */
  @ResolveField('isTrialEnding', () => Boolean, { nullable: true })
  isTrialEnding(@Parent() subscription: CompanySubscriptionDto): boolean {
    try {
      if (!subscription.isFreeTrial || !subscription.subscriptionEndDate) return false;
      const daysLeft = moment(subscription.subscriptionEndDate).diff(moment(), 'days');
      return daysLeft <= 3 && daysLeft >= 0;
    } catch (error) {
      this.logger.error('Error in isTrialEnding resolver:', error);
      return false;
    }
  }

  /**
   * Resolver for daysLeftInTrial computed field
   */
  @ResolveField('daysLeftInTrial', () => Number, { nullable: true })
  daysLeftInTrial(@Parent() subscription: CompanySubscriptionDto): number | null {
    try {
      if (!subscription.isFreeTrial || !subscription.subscriptionEndDate) return null;
      return Math.max(0, moment(subscription.subscriptionEndDate).diff(moment(), 'days'));
    } catch (error) {
      this.logger.error('Error in daysLeftInTrial resolver:', error);
      return null;
    }
  }

  /**
   * Resolver for nextPaymentDate computed field
   */
  @ResolveField('nextPaymentDate', () => Date, { nullable: true })
  nextPaymentDate(@Parent() subscription: CompanySubscriptionDto): Date | null {
    try {
      return subscription.nextBillingDate || subscription.subscriptionEndDate;
    } catch (error) {
      this.logger.error('Error in nextPaymentDate resolver:', error);
      return null;
    }
  }
}
